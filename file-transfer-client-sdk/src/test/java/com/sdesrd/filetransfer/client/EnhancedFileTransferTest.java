package com.sdesrd.filetransfer.client;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.io.TempDir;

import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.ConcurrentTransferManager;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的文件传输测试
 * 测试新增的重试机制、并发控制和性能监控功能
 */
@Slf4j
@DisplayName("增强的文件传输功能测试")
class EnhancedFileTransferTest {
    
    @TempDir
    Path tempDir;
    
    private FileTransferClient client;
    private TestTransferListener listener;
    
    @BeforeEach
    void setUp() {
        // 创建增强的客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("localhost")
                .serverPort(49011)
                .auth("demo", "demo-secret-key-2024")
                .chunkSize(512 * 1024) // 512KB 分块，便于测试
                .maxConcurrentTransfers(3)
                .retry(3, 1000)
                .build();

        // 设置超时时间
        config.setConnectTimeoutSeconds(30);
        config.setReadTimeoutSeconds(60);
        config.setWriteTimeoutSeconds(60);
        
        client = new FileTransferClient(config);
        listener = new TestTransferListener();
        
        log.info("测试环境初始化完成 - 临时目录: {}", tempDir);
    }
    
    @AfterEach
    void tearDown() {
        if (client != null) {
            client.close();
        }
    }
    
    @Test
    @DisplayName("测试并发上传功能")
    @Timeout(value = 2, unit = TimeUnit.MINUTES)
    void testConcurrentUpload() throws Exception {
        // 创建多个测试文件
        File file1 = createTestFile("test-file-1.txt", StringUtils.repeat("这是测试文件1的内容。", 1000));
        File file2 = createTestFile("test-file-2.txt", StringUtils.repeat("这是测试文件2的内容。", 1500));
        File file3 = createTestFile("test-file-3.txt", StringUtils.repeat("这是测试文件3的内容。", 2000));
        
        // 并发上传
        CompletableFuture<UploadResult> future1 = client.uploadFile(file1.getAbsolutePath(), null, listener);
        CompletableFuture<UploadResult> future2 = client.uploadFile(file2.getAbsolutePath(), null, listener);
        CompletableFuture<UploadResult> future3 = client.uploadFile(file3.getAbsolutePath(), null, listener);
        
        // 等待所有上传完成
        UploadResult result1 = future1.get();
        UploadResult result2 = future2.get();
        UploadResult result3 = future3.get();
        
        // 验证结果
        assertTrue(result1.isSuccess(), "文件1上传应该成功");
        assertTrue(result2.isSuccess(), "文件2上传应该成功");
        assertTrue(result3.isSuccess(), "文件3上传应该成功");
        
        assertNotNull(result1.getFileId(), "文件1应该有文件ID");
        assertNotNull(result2.getFileId(), "文件2应该有文件ID");
        assertNotNull(result3.getFileId(), "文件3应该有文件ID");
        
        // 验证传输统计
        ConcurrentTransferManager.TransferStats stats = client.getTransferStats();
        log.info("传输统计: {}", stats);
        
        assertTrue(stats.getTotalUploadedBytes() > 0, "应该有上传字节数统计");
        assertEquals(0, stats.getActiveTransfers(), "所有传输应该已完成");
    }
    
    @Test
    @DisplayName("测试增强的分片下载功能")
    @Timeout(value = 2, unit = TimeUnit.MINUTES)
    void testEnhancedChunkedDownload() throws Exception {
        // 先上传一个大文件
        File uploadFile = createTestFile("large-file.txt", StringUtils.repeat("大文件内容测试。", 5000));
        UploadResult uploadResult = client.uploadFile(uploadFile.getAbsolutePath(), null, listener).get();
        
        assertTrue(uploadResult.isSuccess(), "上传应该成功");
        
        // 使用增强的分片下载
        File downloadFile = tempDir.resolve("downloaded-large-file.txt").toFile();
        DownloadResult downloadResult = client.downloadFileChunk(
                uploadResult.getFileId(), 
                downloadFile.getAbsolutePath(), 
                listener
        ).get();
        
        // 验证下载结果
        assertTrue(downloadResult.isSuccess(), "下载应该成功");
        assertTrue(downloadFile.exists(), "下载文件应该存在");
        assertEquals(uploadFile.length(), downloadFile.length(), "文件大小应该一致");
        
        // 验证文件内容
        String originalContent = new String(Files.readAllBytes(uploadFile.toPath()), StandardCharsets.UTF_8);
        String downloadedContent = new String(Files.readAllBytes(downloadFile.toPath()), StandardCharsets.UTF_8);
        assertEquals(originalContent, downloadedContent, "文件内容应该一致");
        
        log.info("增强分片下载测试完成 - 原文件: {}, 下载文件: {}", 
                FileUtils.formatFileSize(uploadFile.length()),
                FileUtils.formatFileSize(downloadFile.length()));
    }
    
    @Test
    @DisplayName("测试断点续传功能")
    @Timeout(value = 2, unit = TimeUnit.MINUTES)
    void testResumeDownload() throws Exception {
        // 上传文件
        File uploadFile = createTestFile("resume-test.txt", StringUtils.repeat("断点续传测试内容。", 3000));
        UploadResult uploadResult = client.uploadFile(uploadFile.getAbsolutePath(), null, listener).get();
        
        assertTrue(uploadResult.isSuccess(), "上传应该成功");
        
        // 第一次下载（模拟中断）
        File downloadFile = tempDir.resolve("resume-download.txt").toFile();
        
        // 创建部分下载的文件
        String partialContent = StringUtils.repeat("断点续传测试内容。", 1000);
        Files.write(downloadFile.toPath(), partialContent.getBytes(StandardCharsets.UTF_8));
        
        long partialSize = downloadFile.length();
        log.info("模拟部分下载 - 已下载: {}", FileUtils.formatFileSize(partialSize));
        
        // 继续下载
        DownloadResult resumeResult = client.downloadFileChunk(
                uploadResult.getFileId(),
                downloadFile.getAbsolutePath(),
                listener
        ).get();
        
        // 验证断点续传结果
        assertTrue(resumeResult.isSuccess(), "断点续传应该成功");
        assertTrue(downloadFile.length() > partialSize, "文件应该继续下载");
        assertEquals(uploadFile.length(), downloadFile.length(), "最终文件大小应该一致");
        
        log.info("断点续传测试完成 - 最终文件大小: {}", 
                FileUtils.formatFileSize(downloadFile.length()));
    }
    
    @Test
    @DisplayName("测试传输统计和监控功能")
    void testTransferStatistics() throws Exception {
        // 获取初始统计
        ConcurrentTransferManager.TransferStats initialStats = client.getTransferStats();
        log.info("初始统计: {}", initialStats);
        
        // 执行一些传输操作
        File testFile = createTestFile("stats-test.txt", StringUtils.repeat("统计测试内容。", 1000));
        UploadResult uploadResult = client.uploadFile(testFile.getAbsolutePath(), null, listener).get();
        
        assertTrue(uploadResult.isSuccess(), "上传应该成功");
        
        // 下载文件
        File downloadFile = tempDir.resolve("stats-download.txt").toFile();
        DownloadResult downloadResult = client.downloadFile(
                uploadResult.getFileId(),
                downloadFile.getAbsolutePath(),
                listener
        ).get();
        
        assertTrue(downloadResult.isSuccess(), "下载应该成功");
        
        // 获取最终统计
        ConcurrentTransferManager.TransferStats finalStats = client.getTransferStats();
        log.info("最终统计: {}", finalStats);
        
        // 验证统计数据
        assertTrue(finalStats.getTotalUploadedBytes() >= initialStats.getTotalUploadedBytes(), 
                "上传字节数应该增加");
        assertTrue(finalStats.getTotalDownloadedBytes() >= initialStats.getTotalDownloadedBytes(), 
                "下载字节数应该增加");
    }
    
    @Test
    @DisplayName("测试传输槽位管理")
    void testTransferSlotManagement() throws Exception {
        // 检查初始槽位
        assertTrue(client.hasAvailableSlot(), "应该有可用的传输槽位");
        
        // 创建测试文件
        File testFile = createTestFile("slot-test.txt", StringUtils.repeat("槽位测试内容。", 500));
        
        // 启动传输但不等待完成
        CompletableFuture<UploadResult> future = client.uploadFile(testFile.getAbsolutePath(), null, listener);
        
        // 等待传输完成
        UploadResult result = future.get();
        assertTrue(result.isSuccess(), "传输应该成功");
        
        // 传输完成后应该有可用槽位
        assertTrue(client.hasAvailableSlot(), "传输完成后应该有可用槽位");
    }
    
    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, String content) throws IOException {
        File file = tempDir.resolve(fileName).toFile();
        Files.write(file.toPath(), content.getBytes(StandardCharsets.UTF_8));
        return file;
    }
    
    /**
     * 测试传输监听器
     */
    private static class TestTransferListener implements TransferListener {
        private volatile boolean startCalled = false;
        private volatile boolean progressCalled = false;
        private volatile boolean completedCalled = false;
        private volatile boolean errorCalled = false;
        private volatile int progressCallCount = 0;
        
        @Override
        public void onStart(TransferProgress progress) {
            startCalled = true;
            log.debug("传输开始: {}", progress.getFileName());
        }
        
        @Override
        public void onProgress(TransferProgress progress) {
            progressCalled = true;
            progressCallCount++;
            
            if (progressCallCount % 10 == 0) { // 每10次进度更新输出一次日志
                log.debug("传输进度: {}% - {}", 
                        String.format("%.1f", progress.getProgress()),
                        progress.getFileName());
            }
        }
        
        @Override
        public void onCompleted(TransferProgress progress) {
            completedCalled = true;
            log.info("传输完成: {} - {}", 
                    progress.getFileName(),
                    FileUtils.formatFileSize(progress.getTotalSize()));
        }
        
        @Override
        public void onError(TransferProgress progress, Throwable error) {
            errorCalled = true;
            log.error("传输失败: {} - {}", progress.getFileName(), error.getMessage());
        }
        
        // Getters for verification
        public boolean isStartCalled() { return startCalled; }
        public boolean isProgressCalled() { return progressCalled; }
        public boolean isCompletedCalled() { return completedCalled; }
        public boolean isErrorCalled() { return errorCalled; }
        public int getProgressCallCount() { return progressCallCount; }
    }
}
